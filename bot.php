<?php

// Include configuration file
require_once 'config.php';
// Include texts file
require_once 'texts.php';

define('API_KEY', $API_KEY);

function bot($method, $datas = [])
{
    $url = "https://api.telegram.org/bot" . API_KEY . "/" . $method;
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $datas);
    $res = curl_exec($ch);
    if (curl_error($ch)) {
        var_dump(curl_error($ch));
    } else {
        return json_decode($res);
    }
}

function sendmessage($chat_id, $text, $reply_markup = null)
{
    $data = [
        'chat_id' => $chat_id,
        'text' => $text,
        'parse_mode' => 'HTML'
    ];

    if ($reply_markup) {
        $data['reply_markup'] = $reply_markup;
    }

    bot('sendMessage', $data);
}

function checkUserMembership($user_id, $channels)
{
    foreach ($channels as $channel) {
        $result = bot('getChatMember', [
            'chat_id' => $channel,
            'user_id' => $user_id
        ]);

        if (!$result || !isset($result->result) ||
            in_array($result->result->status, ['left', 'kicked'])) {
            return false;
        }
    }
    return true;
}

function createJoinKeyboard($channels)
{
    $keyboard = [];

    // Create a single row with all channel buttons side by side
    $channel_buttons = [];
    $channel_names = ['کانال اول', 'کانال دوم'];

    foreach ($channels as $index => $channel) {
        $channel_name = str_replace('@', '', $channel);
        $button_text = isset($channel_names[$index]) ? $channel_names[$index] : "کانال " . ($index + 1);
        $channel_buttons[] = [
            'text' => "🔔 عضویت در $button_text",
            'url' => "https://t.me/$channel_name"
        ];
    }
    // Reverse the array to put "کانال اول" on the right
    $channel_buttons = array_reverse($channel_buttons);
    $keyboard[] = $channel_buttons;

    // Add check membership button in a separate row
    $keyboard[] = [
        [
            'text' => '🔄 بررسی عضویت',
            'callback_data' => 'check_membership'
        ]
    ];

    return json_encode(['inline_keyboard' => $keyboard]);
}

$update = json_decode(file_get_contents('php://input'));

// Handle callback queries (button presses)
if (isset($update->callback_query)) {
    $callback_query = $update->callback_query;
    $user_id = $callback_query->from->id;
    $chat_id = $callback_query->message->chat->id;
    $data = $callback_query->data;

    if ($data == 'check_membership') {
        $join_keyboard = createJoinKeyboard($required_channels);

        // First, show checking message with buttons
        bot('editMessageText', [
            'chat_id' => $chat_id,
            'message_id' => $callback_query->message->message_id,
            'text' => "⌛️ در حال بررسی عضویت شما...",
            'reply_markup' => $join_keyboard
        ]);

        // Small delay to show the checking message
        sleep(1);

        if (checkUserMembership($user_id, $required_channels)) {
            $first_name = $callback_query->from->first_name ?? 'کاربر';

            // Create main menu keyboard
            $main_menu_keyboard = json_encode([
                'inline_keyboard' => [
                    [
                        ['text' => '👀 حریم خصوصی', 'callback_data' => 'privacy'],
                        ['text' => '💬 بخش نجوا', 'callback_data' => 'najva_section']
                    ],
                    [
                        ['text' => '☎️ پشتیبانی', 'callback_data' => 'support'],
                        ['text' => '📚 راهنما', 'callback_data' => 'help']
                    ],
                    [
                        ['text' => '🇮🇷 زبان', 'callback_data' => 'language']
                    ]
                ]
            ]);

            $welcome_text = BotTexts::getWelcomeText($first_name);

            bot('editMessageText', [
                'chat_id' => $chat_id,
                'message_id' => $callback_query->message->message_id,
                'text' => $welcome_text,
                'parse_mode' => 'HTML',
                'reply_markup' => $main_menu_keyboard
            ]);
        } else {
            bot('editMessageText', [
                'chat_id' => $chat_id,
                'message_id' => $callback_query->message->message_id,
                'text' => BotTexts::getNotMemberText(),
                'parse_mode' => 'HTML',
                'reply_markup' => $join_keyboard
            ]);
        }
    }
    exit;
}

// Handle regular messages
if (isset($update->message)) {
    $message = $update->message;
    $chat_id = $message->chat->id;
    $user_id = $message->from->id;
    $text = $message->text;

    if ($text == "/start") {
        $first_name = $message->from->first_name ?? 'کاربر';

        if (checkUserMembership($user_id, $required_channels)) {
            // Create main menu keyboard
            $main_menu_keyboard = json_encode([
                'inline_keyboard' => [
                    [
                        ['text' => '👀 حریم خصوصی', 'callback_data' => 'privacy'],
                        ['text' => '💬 بخش نجوا', 'callback_data' => 'najva_section']
                    ],
                    [
                        ['text' => '☎️ پشتیبانی', 'callback_data' => 'support'],
                        ['text' => '📚 راهنما', 'callback_data' => 'help']
                    ],
                    [
                        ['text' => '🇮🇷 زبان', 'callback_data' => 'language']
                    ]
                ]
            ]);

            $welcome_text = BotTexts::getWelcomeText($first_name);

            sendmessage($chat_id, $welcome_text, $main_menu_keyboard);
        } else {
            $join_keyboard = createJoinKeyboard($required_channels);
            sendmessage($chat_id,
                BotTexts::getJoinChannelsText($first_name),
                $join_keyboard
            );
        }
    }
}
?>