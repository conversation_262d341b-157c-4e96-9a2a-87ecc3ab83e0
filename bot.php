<?php

// Include configuration file
require_once 'config.php';
// Include texts file
require_once 'texts.php';

define('API_KEY', $API_KEY);

function bot($method, $datas = [])
{
    $url = "https://api.telegram.org/bot" . API_KEY . "/" . $method;
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $datas);
    $res = curl_exec($ch);
    if (curl_error($ch)) {
        var_dump(curl_error($ch));
    } else {
        return json_decode($res);
    }
}

function sendmessage($chat_id, $text, $reply_markup = null)
{
    $data = [
        'chat_id' => $chat_id,
        'text' => $text,
        'parse_mode' => 'HTML'
    ];

    if ($reply_markup) {
        $data['reply_markup'] = $reply_markup;
    }

    bot('sendMessage', $data);
}

function checkUserMembership($user_id, $channels)
{
    foreach ($channels as $channel) {
        $result = bot('getChatMember', [
            'chat_id' => $channel,
            'user_id' => $user_id
        ]);

        if (!$result || !isset($result->result) ||
            in_array($result->result->status, ['left', 'kicked'])) {
            return false;
        }
    }
    return true;
}

function createJoinKeyboard($channels)
{
    $keyboard = [];

    // Create a single row with all channel buttons side by side
    $channel_buttons = [];
    $channel_names = ['کانال اول', 'کانال دوم'];

    foreach ($channels as $index => $channel) {
        $channel_name = str_replace('@', '', $channel);
        $button_text = isset($channel_names[$index]) ? $channel_names[$index] : "کانال " . ($index + 1);
        $channel_buttons[] = [
            'text' => "🔔 عضویت در $button_text",
            'url' => "https://t.me/$channel_name"
        ];
    }
    // Reverse the array to put "کانال اول" on the right
    $channel_buttons = array_reverse($channel_buttons);
    $keyboard[] = $channel_buttons;

    // Add check membership button in a separate row
    $keyboard[] = [
        [
            'text' => '🔄 بررسی عضویت',
            'callback_data' => 'check_membership'
        ]
    ];

    return json_encode(['inline_keyboard' => $keyboard]);
}

$update = json_decode(file_get_contents('php://input'));

// Handle callback queries (button presses)
if (isset($update->callback_query)) {
    $callback_query = $update->callback_query;
    $user_id = $callback_query->from->id;
    $chat_id = $callback_query->message->chat->id;
    $data = $callback_query->data;

    if ($data == 'check_membership') {
        $join_keyboard = createJoinKeyboard($required_channels);

        // First, show checking message with buttons
        bot('editMessageText', [
            'chat_id' => $chat_id,
            'message_id' => $callback_query->message->message_id,
            'text' => "⌛️ در حال بررسی عضویت شما...",
            'reply_markup' => $join_keyboard
        ]);

        // Small delay to show the checking message
        sleep(1);

        if (checkUserMembership($user_id, $required_channels)) {
            $first_name = $callback_query->from->first_name ?? 'کاربر';
            $user_lang = BotTexts::getUserLanguage($user_id);

            $main_menu_keyboard = BotTexts::createMainMenuKeyboard($user_lang);
            $welcome_text = BotTexts::getWelcomeText($first_name, $user_lang);

            bot('editMessageText', [
                'chat_id' => $chat_id,
                'message_id' => $callback_query->message->message_id,
                'text' => $welcome_text,
                'parse_mode' => 'HTML',
                'reply_markup' => $main_menu_keyboard
            ]);
        } else {
            bot('editMessageText', [
                'chat_id' => $chat_id,
                'message_id' => $callback_query->message->message_id,
                'text' => BotTexts::getNotMemberText(),
                'parse_mode' => 'HTML',
                'reply_markup' => $join_keyboard
            ]);
        }
    }

    // Handle language selection
    elseif ($data == 'language') {
        $user_lang = BotTexts::getUserLanguage($user_id);
        $language_keyboard = BotTexts::createLanguageKeyboard($user_lang);
        $language_text = BotTexts::getLanguageText($user_lang);

        bot('editMessageText', [
            'chat_id' => $chat_id,
            'message_id' => $callback_query->message->message_id,
            'text' => $language_text,
            'parse_mode' => 'HTML',
            'reply_markup' => $language_keyboard
        ]);
    }

    // Handle language change to Persian
    elseif ($data == 'set_lang_fa') {
        BotTexts::setUserLanguage($user_id, 'fa');
        $confirmation_text = BotTexts::getLanguageChangedText('fa');

        bot('answerCallbackQuery', [
            'callback_query_id' => $callback_query->id,
            'text' => $confirmation_text,
            'show_alert' => true
        ]);

        // Show main menu with new language
        $first_name = $callback_query->from->first_name ?? 'کاربر';
        $main_menu_keyboard = BotTexts::createMainMenuKeyboard('fa');
        $welcome_text = BotTexts::getWelcomeText($first_name, 'fa');

        bot('editMessageText', [
            'chat_id' => $chat_id,
            'message_id' => $callback_query->message->message_id,
            'text' => $welcome_text,
            'parse_mode' => 'HTML',
            'reply_markup' => $main_menu_keyboard
        ]);
    }

    // Handle language change to English
    elseif ($data == 'set_lang_en') {
        BotTexts::setUserLanguage($user_id, 'en');
        $confirmation_text = BotTexts::getLanguageChangedText('en');

        bot('answerCallbackQuery', [
            'callback_query_id' => $callback_query->id,
            'text' => $confirmation_text,
            'show_alert' => true
        ]);

        // Show main menu with new language
        $first_name = $callback_query->from->first_name ?? 'User';
        $main_menu_keyboard = BotTexts::createMainMenuKeyboard('en');
        $welcome_text = BotTexts::getWelcomeText($first_name, 'en');

        bot('editMessageText', [
            'chat_id' => $chat_id,
            'message_id' => $callback_query->message->message_id,
            'text' => $welcome_text,
            'parse_mode' => 'HTML',
            'reply_markup' => $main_menu_keyboard
        ]);
    }

    // Handle anonymous section
    elseif ($data == 'najva_section') {
        $user_lang = BotTexts::getUserLanguage($user_id);
        $najva_keyboard = BotTexts::createNajvaSectionKeyboard($user_lang);
        $najva_text = BotTexts::getNajvaSectionText($user_lang);

        bot('editMessageText', [
            'chat_id' => $chat_id,
            'message_id' => $callback_query->message->message_id,
            'text' => $najva_text,
            'parse_mode' => 'HTML',
            'reply_markup' => $najva_keyboard
        ]);
    }

    // Handle najva help
    elseif ($data == 'najva_help') {
        $user_lang = BotTexts::getUserLanguage($user_id);
        $help_keyboard = BotTexts::createNajvaHelpKeyboard($user_lang);
        $help_text = BotTexts::getNajvaHelpText($user_lang);

        bot('editMessageText', [
            'chat_id' => $chat_id,
            'message_id' => $callback_query->message->message_id,
            'text' => $help_text,
            'parse_mode' => 'HTML',
            'reply_markup' => $help_keyboard
        ]);
    }

    // Handle najva settings
    elseif ($data == 'najva_settings') {
        $user_lang = BotTexts::getUserLanguage($user_id);
        $settings_keyboard = BotTexts::createNajvaSettingsKeyboard($user_lang);
        $settings_text = BotTexts::getNajvaSettingsText($user_lang);

        bot('editMessageText', [
            'chat_id' => $chat_id,
            'message_id' => $callback_query->message->message_id,
            'text' => $settings_text,
            'parse_mode' => 'HTML',
            'reply_markup' => $settings_keyboard
        ]);
    }

    // Handle back to menu
    elseif ($data == 'back_to_menu') {
        $first_name = $callback_query->from->first_name ?? 'کاربر';
        $user_lang = BotTexts::getUserLanguage($user_id);

        $main_menu_keyboard = BotTexts::createMainMenuKeyboard($user_lang);
        $welcome_text = BotTexts::getWelcomeText($first_name, $user_lang);

        bot('editMessageText', [
            'chat_id' => $chat_id,
            'message_id' => $callback_query->message->message_id,
            'text' => $welcome_text,
            'parse_mode' => 'HTML',
            'reply_markup' => $main_menu_keyboard
        ]);
    }

    exit;
}

// Handle regular messages
if (isset($update->message)) {
    $message = $update->message;
    $chat_id = $message->chat->id;
    $user_id = $message->from->id;
    $text = $message->text;

    if ($text == "/start") {
        $first_name = $message->from->first_name ?? 'کاربر';

        if (checkUserMembership($user_id, $required_channels)) {
            $user_lang = BotTexts::getUserLanguage($user_id);

            $main_menu_keyboard = BotTexts::createMainMenuKeyboard($user_lang);
            $welcome_text = BotTexts::getWelcomeText($first_name, $user_lang);

            sendmessage($chat_id, $welcome_text, $main_menu_keyboard);
        } else {
            $join_keyboard = createJoinKeyboard($required_channels);
            sendmessage($chat_id,
                BotTexts::getJoinChannelsText($first_name),
                $join_keyboard
            );
        }
    }
}
?>