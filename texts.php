<?php

/**
 * All bot texts and messages
 * تمامی متن‌ها و پیام‌های ربات
 */

class BotTexts {

    /**
     * Get user language from file storage
     * دریافت زبان کاربر از فایل
     */
    public static function getUserLanguage($user_id) {
        $lang_file = 'user_languages.json';
        if (file_exists($lang_file)) {
            $languages = json_decode(file_get_contents($lang_file), true);
            return $languages[$user_id] ?? 'fa';
        }
        return 'fa';
    }

    /**
     * Set user language in file storage
     * تنظیم زبان کاربر در فایل
     */
    public static function setUserLanguage($user_id, $lang) {
        $lang_file = 'user_languages.json';
        $languages = [];
        if (file_exists($lang_file)) {
            $languages = json_decode(file_get_contents($lang_file), true);
        }
        $languages[$user_id] = $lang;
        file_put_contents($lang_file, json_encode($languages));
    }

    /**
     * Welcome message text
     * متن پیام خوش آمدگویی
     */
    public static function getWelcomeText($first_name, $lang = 'fa') {
        if ($lang == 'en') {
            return "Hello $first_name 👋\n\n" .
                   "Welcome to Najvagram Bot.\n\n" .
                   "Using this bot, you can send anonymous messages in private chats and groups.\n\n" .
                   "<blockquote>📚 Please read the usage instructions through the help button or /help command.</blockquote>";
        }

        return "سلام $first_name 👋\n\n" .
               "به ربات نجوا گرام خوش آمدید.\n\n" .
               "با استفاده از این ربات شما می توانید متن و یا سایر محتوا را در چت های خصوصی و گروه نجوا کنید.\n\n" .
               "<blockquote>📚 لطفا مراحل استفاده از ربات را از طریق دکمه راهنما و یا دستور /help مطالعه کنید.</blockquote>";
    }
    
    /**
     * Join channels message
     * پیام عضویت در کانال‌ها
     */
    public static function getJoinChannelsText($first_name) {
        return "سلام $first_name 👋\n\n" .
               "برای استفاده از ربات، لطفا ابتدا در کانال‌های زیر عضو شوید و سپس روی دکمه «بررسی عضویت» کلیک کنید:";
    }
    
    /**
     * Not member message
     * پیام عدم عضویت
     */
    public static function getNotMemberText() {
        return "❌ شما هنوز عضو نیستید!\n\n" .
               "برای استفاده از ربات، لطفا ابتدا در کانال‌های زیر عضو شوید و سپس روی دکمه «بررسی عضویت» کلیک کنید:";
    }
    
    /**
     * Help message
     * پیام راهنما
     */
    public static function getHelpText() {
        return "📚 راهنمای استفاده از ربات\n\n" .
               "این ربات برای ارسال پیام‌های ناشناس (نجوا) طراحی شده است.\n\n" .
               "🔹 مراحل استفاده:\n" .
               "1️⃣ روی دکمه «💬 بخش نجوا» کلیک کنید\n" .
               "2️⃣ پیام خود را تایپ کنید\n" .
               "3️⃣ ربات لینک ناشناس برای شما ایجاد می‌کند\n" .
               "4️⃣ لینک را در گروه یا چت مورد نظر ارسال کنید\n\n" .
               "⚠️ نکات مهم:\n" .
               "• پیام‌های شما کاملاً ناشناس هستند\n" .
               "• از ارسال محتوای نامناسب خودداری کنید\n" .
               "• این ربات صرفاً برای سرگرمی طراحی شده است";
    }
    
    /**
     * Privacy message
     * پیام حریم خصوصی
     */
    public static function getPrivacyText() {
        return "👀 حریم خصوصی\n\n" .
               "🔒 ما به حریم خصوصی شما احترام می‌گذاریم:\n\n" .
               "• هیچ اطلاعات شخصی شما ذخیره نمی‌شود\n" .
               "• پیام‌های شما کاملاً ناشناس هستند\n" .
               "• ما دسترسی به محتوای پیام‌های شما نداریم\n" .
               "• تمامی داده‌ها به صورت رمزنگاری شده محافظت می‌شوند\n\n" .
               "در صورت هرگونه سوال، با پشتیبانی تماس بگیرید.";
    }
    
    /**
     * Support message
     * پیام پشتیبانی
     */
    public static function getSupportText() {
        return "☎️ پشتیبانی\n\n" .
               "برای دریافت کمک و پشتیبانی:\n\n" .
               "📞 تماس با ادمین: @speedxteam\n" .
               "📧 کانال اطلاع‌رسانی: @speedx_bots\n\n" .
               "⏰ ساعات پاسخگویی: 9 صبح تا 12 شب\n\n" .
               "لطفاً سوالات خود را به صورت واضح مطرح کنید تا بتوانیم بهترین کمک را به شما ارائه دهیم.";
    }
    
    /**
     * Language message
     * پیام زبان
     */
    public static function getLanguageText($lang = 'fa') {
        if ($lang == 'en') {
            return "🌐 Language Selection\n\n" .
                   "Please choose your preferred language:";
        }

        return "🌐 انتخاب زبان\n\n" .
               "لطفا زبان مورد نظر خود را انتخاب کنید:";
    }

    /**
     * Language changed confirmation
     * تایید تغییر زبان
     */
    public static function getLanguageChangedText($lang = 'fa') {
        if ($lang == 'en') {
            return "✅ Language changed to English successfully!";
        }

        return "✅ زبان با موفقیت به فارسی تغییر یافت!";
    }
    
    /**
     * Najva section message
     * پیام بخش نجوا
     */
    public static function getNajvaSectionText($lang = 'fa') {
        if ($lang == 'en') {
            return "💬 Anonymous Section\n\n" .
                   "Welcome to the anonymous messaging section. Here you can create anonymous messages and manage your settings.\n\n" .
                   "Choose an option below:";
        }

        return "💬 بخش نجوا\n\n" .
               "به بخش پیام‌رسانی ناشناس خوش آمدید. در اینجا می‌توانید پیام‌های ناشناس ایجاد کنید و تنظیمات خود را مدیریت کنید.\n\n" .
               "یکی از گزینه‌های زیر را انتخاب کنید:";
    }

    /**
     * Najva help text
     * متن راهنمای نجوا
     */
    public static function getNajvaHelpText($lang = 'fa') {
        if ($lang == 'en') {
            return "📚 Anonymous Messaging Guide\n\n" .
                   "<b>How to send anonymous messages:</b>\n\n" .
                   "1️⃣ Click on \"✍️ Create Message\"\n" .
                   "2️⃣ Type your anonymous message\n" .
                   "3️⃣ Bot will create an anonymous link for you\n" .
                   "4️⃣ Share the link in your desired group or chat\n\n" .
                   "<b>⚠️ Important Notes:</b>\n" .
                   "• Your messages are completely anonymous\n" .
                   "• No one can identify the sender\n" .
                   "• Avoid sending inappropriate content\n" .
                   "• This bot is designed for entertainment purposes only\n\n" .
                   "<b>🔒 Privacy:</b>\n" .
                   "• We don't store your personal information\n" .
                   "• Messages are encrypted and secure\n" .
                   "• Your identity remains completely protected";
        }

        return "📚 راهنمای پیام‌رسانی ناشناس\n\n" .
               "<b>نحوه ارسال پیام ناشناس:</b>\n\n" .
               "1️⃣ روی \"✍️ ایجاد پیام\" کلیک کنید\n" .
               "2️⃣ پیام ناشناس خود را تایپ کنید\n" .
               "3️⃣ ربات لینک ناشناس برای شما ایجاد می‌کند\n" .
               "4️⃣ لینک را در گروه یا چت مورد نظر به اشتراک بگذارید\n\n" .
               "<b>⚠️ نکات مهم:</b>\n" .
               "• پیام‌های شما کاملاً ناشناس هستند\n" .
               "• هیچ‌کس نمی‌تواند فرستنده را شناسایی کند\n" .
               "• از ارسال محتوای نامناسب خودداری کنید\n" .
               "• این ربات صرفاً برای سرگرمی طراحی شده است\n\n" .
               "<b>🔒 حریم خصوصی:</b>\n" .
               "• اطلاعات شخصی شما ذخیره نمی‌شود\n" .
               "• پیام‌ها رمزنگاری شده و امن هستند\n" .
               "• هویت شما کاملاً محافظت می‌شود";
    }

    /**
     * Najva settings text
     * متن تنظیمات نجوا
     */
    public static function getNajvaSettingsText($lang = 'fa') {
        if ($lang == 'en') {
            return "⚙️ Anonymous Settings\n\n" .
                   "Configure your anonymous messaging preferences:\n\n" .
                   "<b>Current Settings:</b>\n" .
                   "• Language: " . ($lang == 'en' ? 'English 🇺🇸' : 'Persian 🇮🇷') . "\n" .
                   "• Anonymous Mode: Enabled ✅\n" .
                   "• Message Encryption: Active 🔒\n\n" .
                   "Choose an option below to modify your settings:";
        }

        return "⚙️ تنظیمات نجوا\n\n" .
               "تنظیمات پیام‌رسانی ناشناس خود را پیکربندی کنید:\n\n" .
               "<b>تنظیمات فعلی:</b>\n" .
               "• زبان: " . ($lang == 'en' ? 'English 🇺🇸' : 'فارسی 🇮🇷') . "\n" .
               "• حالت ناشناس: فعال ✅\n" .
               "• رمزنگاری پیام: فعال 🔒\n\n" .
               "برای تغییر تنظیمات، یکی از گزینه‌های زیر را انتخاب کنید:";
    }
    
    /**
     * Button texts
     * متن دکمه‌ها
     */
    public static function getButtons($lang = 'fa') {
        if ($lang == 'en') {
            return [
                'najva_section' => '💬 Anonymous Section',
                'privacy' => '👀 Privacy',
                'help' => '📚 Help',
                'support' => '☎️ Support',
                'language' => '🌐 Language',
                'check_membership' => '🔄 Check Membership',
                'back_to_menu' => '🔙 Back to Main Menu',
                'persian' => '🇮🇷 فارسی',
                'english' => '🇺🇸 English',
                'create_message' => '✍️ Create Message',
                'najva_help' => '📚 Guide',
                'najva_settings' => '⚙️ Settings',
                'change_language' => '🌐 Change Language'
            ];
        }

        return [
            'najva_section' => '💬 بخش نجوا',
            'privacy' => '👀 حریم خصوصی',
            'help' => '📚 راهنما',
            'support' => '☎️ پشتیبانی',
            'language' => '🌐 زبان',
            'check_membership' => '🔄 بررسی عضویت',
            'back_to_menu' => '🔙 بازگشت به منو اصلی',
            'persian' => '🇮🇷 فارسی',
            'english' => '🇺🇸 English',
            'create_message' => '✍️ ایجاد پیام',
            'najva_help' => '📚 راهنما',
            'najva_settings' => '⚙️ تنظیمات',
            'change_language' => '🌐 تغییر زبان'
        ];
    }

    /**
     * Create main menu keyboard
     * ایجاد کیبورد منو اصلی
     */
    public static function createMainMenuKeyboard($lang = 'fa') {
        $buttons = self::getButtons($lang);

        return json_encode([
            'inline_keyboard' => [
                [
                    ['text' => $buttons['privacy'], 'callback_data' => 'privacy'],
                    ['text' => $buttons['najva_section'], 'callback_data' => 'najva_section']
                ],
                [
                    ['text' => $buttons['support'], 'callback_data' => 'support'],
                    ['text' => $buttons['help'], 'callback_data' => 'help']
                ],
                [
                    ['text' => $buttons['language'], 'callback_data' => 'language']
                ]
            ]
        ]);
    }

    /**
     * Create language selection keyboard
     * ایجاد کیبورد انتخاب زبان
     */
    public static function createLanguageKeyboard($lang = 'fa') {
        $buttons = self::getButtons($lang);

        return json_encode([
            'inline_keyboard' => [
                [
                    ['text' => $buttons['persian'], 'callback_data' => 'set_lang_fa'],
                    ['text' => $buttons['english'], 'callback_data' => 'set_lang_en']
                ],
                [
                    ['text' => $buttons['back_to_menu'], 'callback_data' => 'back_to_menu']
                ]
            ]
        ]);
    }

    /**
     * Create anonymous section keyboard
     * ایجاد کیبورد بخش نجوا
     */
    public static function createNajvaSectionKeyboard($lang = 'fa') {
        $buttons = self::getButtons($lang);

        return json_encode([
            'inline_keyboard' => [
                [
                    ['text' => $buttons['create_message'], 'callback_data' => 'create_message']
                ],
                [
                    ['text' => $buttons['najva_help'], 'callback_data' => 'najva_help'],
                    ['text' => $buttons['najva_settings'], 'callback_data' => 'najva_settings']
                ],
                [
                    ['text' => $buttons['back_to_menu'], 'callback_data' => 'back_to_menu']
                ]
            ]
        ]);
    }

    /**
     * Create najva help keyboard
     * ایجاد کیبورد راهنمای نجوا
     */
    public static function createNajvaHelpKeyboard($lang = 'fa') {
        $buttons = self::getButtons($lang);

        return json_encode([
            'inline_keyboard' => [
                [
                    ['text' => $buttons['create_message'], 'callback_data' => 'create_message']
                ],
                [
                    ['text' => $buttons['back_to_menu'], 'callback_data' => 'back_to_menu']
                ]
            ]
        ]);
    }

    /**
     * Create najva settings keyboard
     * ایجاد کیبورد تنظیمات نجوا
     */
    public static function createNajvaSettingsKeyboard($lang = 'fa') {
        $buttons = self::getButtons($lang);

        return json_encode([
            'inline_keyboard' => [
                [
                    ['text' => $buttons['change_language'], 'callback_data' => 'language']
                ],
                [
                    ['text' => $buttons['back_to_menu'], 'callback_data' => 'back_to_menu']
                ]
            ]
        ]);
    }
}

?>
